import axios from "axios"
import { ref } from "vue"

/**
 * 离线搜索数据接口定义
 */
interface OfflineSearchItem {
  id: number
  name: string
  type: string
  telephone: string
  address: string
  coordinates: number[]
  longitude: number
  latitude: number
  lng: number  
  lat: number  
  _graphic: {
    longitude: number
    latitude: number
  }
}

/**
 * 搜索结果接口定义
 */
interface SearchResult {
  list: OfflineSearchItem[]
  allcount?: number
}

// 使用 ref 缓存数据
const offlineDataCache = ref<OfflineSearchItem[]>([])  
const searchResultCache = ref<Map<string, OfflineSearchItem[]>>(new Map()) 

/**
 * 加载离线搜索数据
 * 从 public/config/data.json 加载 GeoJSON 格式的数据并转换为搜索格式
 * 使用 ref 缓存，避免重复请求
 */
const loadOfflineData = async (): Promise<void> => {
  // 如果已有缓存数据，直接返回
  if (offlineDataCache.value.length > 0) {
    console.log("使用缓存数据，共", offlineDataCache.value.length, "条记录")
    return
  }

  try {
    const response = await axios.get(`${import.meta.env.BASE_URL}config/POIall.json`)

    if (response.data && response.data.features) {
      // 处理数据并直接缓存
      offlineDataCache.value = response.data.features.map((feature: any, index: number): OfflineSearchItem => {
        const lng = feature.properties.POINT_X || feature.geometry.coordinates[0]
        const lat = feature.properties.POINT_Y || feature.geometry.coordinates[1]

        return {
          id: feature.id || index,
          name: feature.properties.NAME || "",
          type: feature.properties.LX || "",
          telephone: feature.properties.TELEPHONE || "",
          address: feature.properties.ADDRESS || "",
          coordinates: feature.geometry.coordinates || [],
          longitude: lng,
          latitude: lat,
          lng: lng, 
          lat: lat, 
          _graphic: {
            longitude: lng,
            latitude: lat
          }
        }
      })

      console.log("离线搜索数据加载成功，共", offlineDataCache.value.length, "条记录")
    }
  } catch (error) {
    console.error("加载离线搜索数据失败:", error)
    throw error
  }
}

/**
 * 离线搜索函数 - 用于搜索提示
 *
 * @param keyword 搜索关键词
 * @returns 返回匹配的搜索结果，格式与原 mapWork.queryData 兼容
 */
export const offlineQueryData = async (keyword: string): Promise<SearchResult> => {
  await loadOfflineData()

  if (!keyword.trim()) {
    return { list: [] }
  }

  // 检查搜索结果缓存
  const cacheKey = keyword.toLowerCase()
  if (searchResultCache.value.has(cacheKey)) {
    console.log("使用搜索缓存:", cacheKey)
    return { list: searchResultCache.value.get(cacheKey)! }
  }

  // 支持按名称、类型、地址进行模糊搜索
  const filteredData = offlineDataCache.value.filter((item: OfflineSearchItem) =>
    item.name.toLowerCase().includes(keyword.toLowerCase()) ||
    item.type.toLowerCase().includes(keyword.toLowerCase()) ||
    item.address.toLowerCase().includes(keyword.toLowerCase())
  )

  // 缓存搜索结果
  searchResultCache.value.set(cacheKey, filteredData)

  return { list: filteredData }
}

/**
 * 离线搜索函数 - 用于详细搜索结果（无分页）
 * 替换 index.vue 中 querySiteList 函数里的 mapWork.querySiteList(text, page) 调用
 *
 * @param keyword 搜索关键词
 * @returns 返回所有匹配的搜索结果，格式与原 mapWork.querySiteList 兼容
 */
export const offlineQuerySiteList = async (keyword: string): Promise<SearchResult> => {
  await loadOfflineData()

  if (!keyword.trim()) {
    return { list: [], allcount: 0 }
  }

  // 检查搜索结果缓存
  const cacheKey = keyword.toLowerCase()
  if (searchResultCache.value.has(cacheKey)) {
    console.log("使用搜索缓存:", cacheKey)
    const cachedData = searchResultCache.value.get(cacheKey)!
    return {
      list: cachedData,
      allcount: cachedData.length
    }
  }

  // 支持按名称、类型、地址进行模糊搜索
  const filteredData = offlineDataCache.value.filter((item: OfflineSearchItem) =>
    item.name.toLowerCase().includes(keyword.toLowerCase()) ||
    item.type.toLowerCase().includes(keyword.toLowerCase()) ||
    item.address.toLowerCase().includes(keyword.toLowerCase())
  )

  // 缓存搜索结果
  searchResultCache.value.set(cacheKey, filteredData)

  return {
    list: filteredData,
    allcount: filteredData.length
  }
}

