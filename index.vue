


<template>
  <mars-dialog :draggable="false" customClass="query-poi-pannel" top="10" left="10">
    <div class="query-poi" @mousedown="clickVoid">
      <div class="query-poi__search">
        <mars-input
          placeholder="搜索 地点"
          v-model:value="searchTxt"
          class="input"
          data-event="prevent"
          allowClear
          @keyup.enter="selectPoint(searchTxt)"
          @focus="handleInputFocus"
          @input="handleInputChange"
        ></mars-input>
        <mars-button class="button">
          <mars-icon icon="search" width="20" color="#fff" @click="selectPoint(searchTxt)"></mars-icon>
        </mars-button>
      </div>

      <!-- 搜索提示列表已注释 -->
      <!-- <ul class="search-list" v-if="searchListShow">
        <li v-for="(item, i) in dataSource" :key="i" class="search-list__item" @click="selectPoint(item.value)">
          {{ item.value }}
        </li>
      </ul> -->

      <div class="query-site" v-if="siteListShow">
        <template v-if="siteSource && siteSource.length">
          <ul>
            <li v-for="(item, i) in siteSource" :key="i" class="query-site__item" @click.stop="flyTo(item)">
              <div class="query-site__context">
                <p class="query-site-text f-toe" :title="item.name">
                  <span class="query-site-text_num">{{ i + 1 }}</span>
                  {{ item.name }}
                </p>
                <p class="query-site-sub f-toe">{{ item.type }}</p>
              </div>
              <!-- <a :href="url + item.id" target="_blank" class="query-site__more">
                <mars-icon icon="double-right" width="20"></mars-icon>
              </a> -->
            </li>
          </ul>
          <div class="query-site__page">
            <p class="query-site-allcount">共{{ allCount }}条结果</p>
            <!-- 修改：使用动态pageSize变量，并使用currentSearchKeyword确保分页一致性 -->
            <a-pagination @change="(page: number) => querySiteList(currentSearchKeyword, page)" size="small" :total="allCount" :pageSize="pageSize" :simple="true" />
          </div>
        </template>
        <a-empty class="f-push-10-t" v-else />
      </div>
    </div>
  </mars-dialog>
</template>

<script lang="ts" setup>
import axios from "axios"
import { ref } from "vue"
import { isLonLat } from "@mars/utils/mars-util"
import useLifecycle from "@mars/common/uses/use-lifecycle"
import * as mapWork from "./map"
import { $message, $alert } from "@mars/components/mars-ui/index"
import { $hideLoading, $showLoading } from "@mars/components/mars-ui/mars-loading"

// ============ 离线搜索功能导入 ============
import {
  // 搜索提示功能已注释，不再需要
  // offlineQueryData,
  offlineQuerySiteList
} from "./searchoutline"
// ========================================

// 启用map.ts生命周期
useLifecycle(mapWork)

// 历史记录功能已注释
// const storageName = "mars3d_queryGaodePOI"
const siteListShow = ref(false)

// 各类数据
const searchTxt = ref("")
// 搜索提示数据已注释
// const dataSource = ref<any[]>([])
// 搜索提示显示状态已注释
// const searchListShow = ref<boolean>(false)
const siteSource = ref<any[]>([])

const allCount = ref(0)
const url = "//www.amap.com/detail/"

// 新增：分页相关状态变量
// 存储完整搜索结果，用于前端分页
const fullSearchResults = ref<any[]>([])
// 当前搜索关键词，用于判断是否为新搜索
const currentSearchKeyword = ref("")
// 当前页码
const currentPage = ref(1)
// 每页显示数量，保持与原配置一致
const pageSize = 6

// 搜索提示定时器已注释
// let timer

// 搜索提示关闭功能已注释
// const startCloseSearch = () => {
//   timer = setTimeout(() => {
//     searchListShow.value = false
//     clearTimeout(timer)
//     timer = null
//   }, 500)
//   // 时间太短会导致点击失败
// }

// 搜索提示功能已注释 - 原来用于显示搜索建议和历史记录
// const handleSearch = async (val: string) => {
//   if (val === "") {
//     showHistoryList()
//     mapWork.clearLayers()
//     return
//   }

//   if (isLonLat(val)) {
//     mapWork.centerAtLonLat(val)
//     return
//   }

//   siteListShow.value = false

//   // ============ 原API调用已注释，替换为离线搜索 ============
//   // const result = await mapWork.queryData(val)
//   // 使用离线搜索数据函数
//   const result = await offlineQueryData(val)
//   // ======================================================
//   const list: { value: string }[] = []

//   result.list.forEach((item: any) => {
//     if (list.every((l) => l.value !== item.name)) {
//       list.push({
//         value: item.name
//       })
//     }
//   })

//   dataSource.value = list
//   searchListShow.value = true
// }

// 历史记录功能已注释
// const showHistoryList = () => {
//   if (searchTxt.value) {
//     return
//   }
//   const historys = JSON.parse(localStorage.getItem(storageName)!)
//   if (historys) {
//     dataSource.value = (historys || []).map((item: any) => ({ value: item }))
//     searchListShow.value = true
//   }
//   if (timer) {
//     clearTimeout(timer)
//   }
//   siteListShow.value = false
// }

// 新增：处理输入框焦点事件 - 当点击输入框时隐藏搜索结果
const handleInputFocus = () => {
  // 如果输入框为空，隐藏搜索结果
  if (!searchTxt.value || !searchTxt.value.trim()) {
    siteListShow.value = false
    // 清除地图上的标记
    mapWork.clearLayers()
  }
}

// 新增：处理输入框内容变化 - 当输入框内容为空时隐藏搜索结果
const handleInputChange = (value: string) => {
  // 如果输入框为空或只有空格，隐藏搜索结果并清除地图标记
  if (!value || !value.trim()) {
    siteListShow.value = false
    mapWork.clearLayers()
    // 清空分页相关状态
    fullSearchResults.value = []
    currentSearchKeyword.value = ""
    allCount.value = 0
    currentPage.value = 1
  }
}

// 开始查询并加载数据
const selectPoint = async (value: any) => {
  // 如果输入为空或只有空格，不执行搜索
  if (!value || !value.trim()) {
    return
  }

  searchTxt.value = value

  // 检查是否为经纬度坐标
  if (isLonLat(value)) {
    mapWork.centerAtLonLat(value)
    return
  }

  $showLoading()
  // 历史记录功能已注释
  // addHistory(value)
  console.log("开始搜索", value)

  // 修改：重置到第一页，确保新搜索从第一页开始
  await querySiteList(value, 1)

  $hideLoading()
  siteListShow.value = true
  // 搜索提示功能已注释
  // searchListShow.value = false
}

// 表格数据内部
const pagination = {
  onChange: (page: number) => {
    // 修改：使用当前搜索关键词进行分页，避免重复搜索
    querySiteList(currentSearchKeyword.value, page)
  },
  size: "small",
  total: 0,
  // 修改：使用常量，保持一致性
  pageSize: pageSize,
  simple: true
}

function clickVoid(e) {
  if (e.target.dataset?.event !== "prevent") {
    e.preventDefault()
  }
}

async function querySiteList(text: string, page: number) {
  // 修改：实现智能分页 - 如果是新的搜索关键词，需要重新获取完整数据
  if (currentSearchKeyword.value !== text) {
    console.log("新搜索，获取完整数据:", text)

    // ============ 原API调用已注释，替换为离线搜索 ============
    // const result = await mapWork.querySiteList(text, page)
    // 使用离线搜索，获取所有结果
    const result = await offlineQuerySiteList(text)
    // ======================================================

    if (!result || !result.list || result.list.length <= 0) {
      $message("暂无数据")
      // 修改：清空分页相关缓存
      fullSearchResults.value = []
      siteSource.value = []
      allCount.value = 0
      currentSearchKeyword.value = ""
      return
    }

    // 修改：缓存完整结果到组件状态
    fullSearchResults.value = result.list
    currentSearchKeyword.value = text
    allCount.value = result.list.length

    // 修改：在地图上显示所有结果点（保持原有功能）
    mapWork.showPOIArr(result.list)
  } else {
    console.log("相同搜索，使用缓存数据分页:", text, "页码:", page)
  }

  // 修改：计算当前页的数据切片
  const startIndex = (page - 1) * pageSize
  const endIndex = page * pageSize
  const pagedResults = fullSearchResults.value.slice(startIndex, endIndex)

  // 修改：更新显示数据为分页结果
  siteSource.value = pagedResults
  currentPage.value = page

  // 修改：更新分页组件的总数
  pagination.total = allCount.value

  return { list: pagedResults, allcount: allCount.value }
}

// 定位至矢量图层
function flyTo(item: any) {
  const graphic = item._graphic
  if (graphic === null) {
    return $alert(item.name + " 无经纬度坐标信息！")
  }

  mapWork.flyToGraphic(graphic, { radius: 2000 })
}

// 历史记录功能已注释
/**
 * 将需要搜查的关键字记录进历史数据中
 * @param {any} data 输入框输入的关键字
 * @returns {void} 无
 */
// function addHistory(data: any) {
//   try {
//     const arrHistory = JSON.parse(localStorage.getItem(storageName)!) || []
//     if (!arrHistory.includes(data)) {
//       arrHistory.unshift(data)
//     }
//     localStorage.setItem(storageName, JSON.stringify(arrHistory.slice(0, 10)))
//   } catch (err: any) {
//     throw new Error(err)
//   }
// }
</script>

<style lang="less">
.query-poi-pannel {
  background: none !important;
  border: none !important;
  padding: 0 !important;
  overflow: visible !important;
}
.query-poi-pannel .mars-dialog__content {
  padding: 0 !important;
}
</style>
<style lang="less" scoped>
.query-poi {
  padding: 0;
  color: #fff;
  .query-poi__search {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 320px;
    height: 44px;
    border: 1px solid var(--mars-primary-color);
    background: var(--mars-bg-base);
    .input {
      border: none;
      background: none;
      height: 44px;
      outline: none;
      padding-left: 10px;
      flex-grow: 1;
      :deep(.ant-input) {
        font-size: 16px;
        color: var(--mars-text-color) !important;
      }
    }
    .button {
      height: 44px;
      width: 55px;
    }
  }
}
/* 搜索提示列表样式已注释 */
/* .search-list {
  min-height: 100px;
  width: 100%;
  .mars-drop-bg();
  position: absolute;
  .search-list__item {
    height: 36px;
    line-height: 36px;
    padding-left: 10px;
    color: var(--mars-text-color);
    cursor: pointer;
    &:hover {
      background-color: var(--mars-select-bg);
    }
  }
} */
.query-site {
  position: absolute;
  border-top: none;
  padding-bottom: 10px;
  width: 100%;
  z-index: 100;
  .mars-drop-bg();

  .query-site__item {
    height: 80px;
    padding: 0 20px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    &:hover {
    po
      background-color: var(--mars-select-bg);
    }
    .query-site__context {
      flex-grow: 1;
      .query-site-text {
        font-size: 16px;
        width: 200px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: var(--mars-text-color);
        .query-site-text_num {
          width: 19px;
          height: 25px;
          margin-right: 5px;
          display: inline-block;
          text-align: center;
          background-image: url("@mars/components/mars-ui/assets/images/query-site-text_num.png");
        }
      }
      .query-site-sub {
        font-size: 14px;
        width: 200px;
        margin-left: 19px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: var(--mars-content-color);
      }
    }
    .query-site__more {
      font-size: 14px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: var(--mars-content-color);
    }
  }
  .query-site__page {
    display: flex;
    justify-content: space-between;
    padding: 10px 20px;
    .query-site-allcount {
      font-size: 14px;
      color: var(--mars-text-color);
    }
  }
}
:deep(.ant-pagination-simple-pager) {
  input {
    width: 50px;
  }
}

:deep(.ant-input-clear-icon) {
  color: var(--mars-content-color) !important;
}
</style>
